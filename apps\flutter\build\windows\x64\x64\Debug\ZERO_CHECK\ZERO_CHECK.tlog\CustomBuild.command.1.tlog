^D:\CODE\DRIFT\APPS\FLUTTER\BUILD\WINDOWS\X64\CMAKEFILES\5FC144546D5EAB19D052945C97A1BCA7\GENERATE.STAMP.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/code/drift/apps/flutter/windows -BD:/code/drift/apps/flutter/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/code/drift/apps/flutter/build/windows/x64/drift.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
