import 'package:flutter/foundation.dart';
import 'package:core/core.dart';
import 'package:uuid/uuid.dart';

import '../models/task.dart';

/// Provider for managing task state in Flutter
class TaskProvider extends ChangeNotifier {
  final Core _core;
  final List<Task> _tasks = [];
  bool _isLoading = false;
  String? _error;
  
  TaskProvider(this._core) {
    _initialize();
  }
  
  /// Get the list of tasks
  List<Task> get tasks => List.unmodifiable(_tasks);
  
  /// Get loading state
  bool get isLoading => _isLoading;
  
  /// Get error message
  String? get error => _error;
  
  /// Get node ID
  String get nodeId => _core.nodeId;

  /// Get known nodes count
  int get knownNodesCount => _core.sync.knownNodes.length;
  
  /// Initialize the provider
  Future<void> _initialize() async {
    await _loadTasks();
    
    // Listen to new events from the event store
    // Note: This would need to be implemented in the EventStore
    // _driftCore.database.eventStream.listen(_onNewEvent);
  }
  
  /// Load tasks from the database
  Future<void> _loadTasks() async {
    _setLoading(true);
    _setError(null);
    
    try {
      // Get all events and reconstruct tasks
      final allEvents = await _core.database.getAllEvents();
      final taskEvents = allEvents.where((event) => event.entityType == 'task').toList();
      
      // Group events by task ID
      final Map<String, List<Event>> taskEventGroups = {};
      for (final event in taskEvents) {
        taskEventGroups.putIfAbsent(event.entityId, () => []).add(event);
      }
      
      // Reconstruct tasks
      _tasks.clear();
      for (final entry in taskEventGroups.entries) {
        final taskState = _reconstructTaskState(entry.key, entry.value);
        if (taskState != null) {
          _tasks.add(Task.fromMap(taskState));
        }
      }
      
      // Sort by creation date
      _tasks.sort((a, b) => a.createdAt.compareTo(b.createdAt));
      
    } catch (e) {
      _setError('Failed to load tasks: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Create a new task
  Future<void> createTask(String title, String description) async {
    _setLoading(true);
    _setError(null);
    
    try {
      const uuid = Uuid();
      final taskId = uuid.v4();
      
      // Create event
      await _core.database.insertEvent(
        Event(
          id: uuid.v4(),
          origin: _core.nodeId,
          timestamp: DateTime.now(),
          entityType: 'task',
          entityId: taskId,
          eventType: 'task_created',
          payload: {
            'title': title,
            'description': description,
          },
        ),
      );

      // Reload tasks
      await _loadTasks();

      // Trigger sync
      await _core.sync.syncNow();
      
    } catch (e) {
      _setError('Failed to create task: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Complete a task
  Future<void> completeTask(String taskId) async {
    _setLoading(true);
    _setError(null);
    
    try {
      const uuid = Uuid();
      
      // Create completion event
      await _core.database.insertEvent(
        Event(
          id: uuid.v4(),
          origin: _core.nodeId,
          timestamp: DateTime.now(),
          entityType: 'task',
          entityId: taskId,
          eventType: 'task_completed',
          payload: {},
        ),
      );

      // Reload tasks
      await _loadTasks();

      // Trigger sync
      await _core.sync.syncNow();
      
    } catch (e) {
      _setError('Failed to complete task: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Refresh tasks and sync
  Future<void> refresh() async {
    await _core.sync.syncNow();
    await _loadTasks();
  }
  
  /// Reconstruct task state from events
  Map<String, dynamic>? _reconstructTaskState(String taskId, List<Event> events) {
    if (events.isEmpty) return null;
    
    // Sort events by timestamp
    events.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    
    Map<String, dynamic> state = {};
    
    for (final event in events) {
      switch (event.eventType) {
        case 'task_created':
          state = {
            'id': taskId,
            'title': event.payload['title'],
            'description': event.payload['description'] ?? '',
            'completed': false,
            'created_at': event.timestamp.toIso8601String(),
            'updated_at': event.timestamp.toIso8601String(),
          };
          break;
          
        case 'task_updated':
          state.addAll(event.payload);
          state['updated_at'] = event.timestamp.toIso8601String();
          break;
          
        case 'task_completed':
          state['completed'] = true;
          state['completed_at'] = event.timestamp.toIso8601String();
          state['updated_at'] = event.timestamp.toIso8601String();
          break;
          
        case 'task_deleted':
          return null; // Task was deleted
      }
    }
    
    return state.isEmpty ? null : state;
  }
  
  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  /// Set error message
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }
  

}
