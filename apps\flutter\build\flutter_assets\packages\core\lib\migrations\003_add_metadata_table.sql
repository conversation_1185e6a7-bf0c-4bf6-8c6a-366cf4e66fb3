-- Add metadata table for storing application metadata
-- This table can store configuration, node information, etc.

CREATE TABLE IF NOT EXISTS metadata (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now'))
);

-- Create trigger to automatically update updated_at timestamp
CREATE TRIGGER IF NOT EXISTS update_metadata_timestamp AFTER UPDATE ON metadata FOR EACH ROW BEGIN UPDATE metadata SET updated_at = datetime('now') WHERE key = NEW.key; END;
