/// Global configuration for the system
class Configuration {
  // Database configuration
  static const String databaseName = 'drift.db';

  /// Get the current database version from migrations
  /// This will be determined dynamically based on available migration files
  static int get databaseVersion {
    try {
      // Import here to avoid circular dependencies
      return _getDatabaseVersionFromMigrations();
    } catch (e) {
      // Fallback to version 1 if migrations can't be read
      print('Warning: Could not determine database version from migrations: $e');
      return 1;
    }
  }

  /// Internal method to get version from migrations
  /// This is a placeholder - the actual implementation will be in DatabaseManager
  static int _getDatabaseVersionFromMigrations() {
    // This will be replaced by MigrationManager.getLatestVersion() in DatabaseManager
    return 3; // Current number of migration files
  }

  // API configuration
  static const int defaultApiPort = 4547;
  static const String apiHost = '0.0.0.0';

  // Sync configuration
  static const Duration syncInterval = Duration(seconds: 15);
  static const Duration syncTimeout = Duration(seconds: 30);

  // Tailscale configuration
  static const String tailscaleApiBaseUrl = 'https://api.tailscale.com/api/v2';
  static const Duration tailscaleTimeout = Duration(seconds: 10);

  // Node configuration
  static const String nodeIdPrefix = 'node';

  // Event configuration
  static const int maxEventsPerSync = 1000;
  static const Duration eventRetentionPeriod = Duration(days: 30);

  // Logging configuration
  static const bool enableDebugLogging = true;
  static const bool enableApiLogging = true;

  // Application configuration
  static const String appName = 'Drift';
  static const String appVersion = '1.0.0';

  // File paths
  static const String configFileName = 'config.json';
  static const String logFileName = 'app.log';

  // Network configuration
  static const Duration httpTimeout = Duration(seconds: 30);
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 2);
}
